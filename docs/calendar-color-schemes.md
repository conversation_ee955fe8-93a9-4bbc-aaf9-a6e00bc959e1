# Calendar Color Schemes

The calendar interface now supports two different color schemes for displaying reservations, allowing users to view the data in the most meaningful way for their needs.

## Color Scheme Options

### 1. Color by Status (Default)
Reservations are colored based on their booking status:
- **Yellow (#fbbf24)** - Pending reservations
- **Light Blue (#23b7e5)** - Confirmed reservations  
- **<PERSON> (#8c9097)** - Completed or past reservations

### 2. Color by Field
Reservations are colored based on which field they are booked for:
- **<PERSON> (#3b82f6)** - Field 1
- **Red (#ef4444)** - Field 2
- **Green (#10b981)** - Field 3
- **Amber (#f59e0b)** - Field 4
- **<PERSON> (#8b5cf6)** - Field 5
- **<PERSON><PERSON> (#06b6d4)** - Field 6
- **Orange (#f97316)** - Field 7
- **<PERSON>e (#84cc16)** - Field 8

*Note: Colors cycle through the palette for additional fields*

## Features

### Color Scheme Selector
- Located in the top-left of the calendar legend area
- Dropdown with two options: "Color by Status" and "Color by Field"
- Changes take effect immediately when selected

### Dynamic Legend
- Updates automatically based on the selected color scheme
- Shows relevant color meanings for the current scheme
- Positioned prominently above the calendar

### Persistent Selection
- User's color scheme preference is saved in browser localStorage
- Selection persists across browser sessions
- Automatically restored when returning to the calendar

### Past Reservations
- Past reservations are always shown in muted gray (#8c9097) regardless of color scheme
- This provides clear visual distinction between current/future and past bookings

## Technical Implementation

### Frontend
- JavaScript `ColorSchemeManager` handles scheme switching and persistence
- Dynamic legend updates without page refresh
- localStorage integration for preference persistence

### Backend
- `CalendarController::events()` accepts `color_scheme` parameter
- Server-side color determination based on scheme selection
- Field-based coloring uses consistent color palette

### API Parameters
- `color_scheme=status` - Use status-based colors (default)
- `color_scheme=field` - Use field-based colors

## Usage Examples

### Viewing by Status
Ideal for:
- Tracking reservation approval workflow
- Identifying pending reservations that need attention
- Monitoring booking status distribution

### Viewing by Field
Ideal for:
- Visualizing field utilization patterns
- Identifying busy vs. underutilized fields
- Planning field maintenance schedules
- Balancing bookings across facilities

## Browser Compatibility
- Works in all modern browsers
- Graceful fallback to status-based coloring if localStorage is unavailable
- No additional dependencies required
