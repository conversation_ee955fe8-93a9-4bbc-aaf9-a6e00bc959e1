@extends('layouts.admin')

@section('title', 'Reservations Calendar - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="bx bx-calendar-alt me-2"></i>Reservations Calendar
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend -->
                    <div class="calendar-legend mb-4 p-3 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                            <div>
                                <h6 class="fw-semibold mb-2 text-muted">Legend:</h6>
                                <div class="d-flex flex-wrap gap-3 fs-12">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">&nbsp;</span>
                                        <span>Pending</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-secondary me-2">&nbsp;</span>
                                        <span>Confirmed</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge" style="background-color: #8c9097;">&nbsp;</span>
                                        <span class="ms-2">Completed (Past Reservations)</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <h6 class="fw-semibold mb-2 text-muted"><i class="ti ti-filter me-1"></i>Filter by field:</h6>
                                <select id="fieldFilter" class="form-select form-select-sm fs-12" style="min-width: 150px;">
                                    <option value="">All Fields</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}">{{ $field->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5" style="min-height: 600px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Restriction Information Modal -->
    <x-confirmation-modal
        modal-id="timeRestrictionModal"
        type="info"
        title="Reservations are only available between 07:00 and 23:00 (opening hours)"
        warning-text="Please select a time slot within our operating hours."
        dismiss-text="OK"
        form-action="#"
    />

    <!-- Reservation Details Modal -->
    <x-reservation-details-modal />

    <!-- Reservation Modal JavaScript -->
    <script src="{{ asset('assets/js/reservation-modal.js') }}"></script>

    <!-- FullCalendar: using local assets from layout (avoid duplicating CDN) -->
    @push('scripts')
        <!-- Locale for DD/MM/YYYY format -->
        <script src="{{ asset('assets/libs/fullcalendar/locales/en-gb.js') }}"></script>
    @endpush


    <!-- Custom FullCalendar Dark Mode Styles -->
    <style>
        /* FullCalendar Dark Mode Weekday Header Fix */
        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell .fc-col-header-cell-cushion {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Additional dark mode calendar styling for consistency */
        [data-theme-mode="dark"] .fc-theme-standard .fc-scrollgrid {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard td,
        [data-theme-mode="dark"] .fc-theme-standard th {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day-number {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day.fc-day-today {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary {
            background-color: rgb(var(--primary-rgb)) !important;
            border-color: rgb(var(--primary-rgb)) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary:hover {
            background-color: rgba(var(--primary-rgb), 0.9) !important;
            border-color: rgba(var(--primary-rgb), 0.9) !important;
        }

        [data-theme-mode="dark"] .fc .fc-toolbar-title {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* SPECIFIC FIX: Target .fc-scrollgrid-section-sticky class causing white background */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            background-image: none !important;
            background: rgb(var(--body-bg-rgb2)) !important;
        }

        /* Additional targeting for sticky header elements */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky .fc-col-header-cell,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky th,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky td {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Legend dark mode styling */
        [data-theme-mode="dark"] .calendar-legend {
            background-color: rgb(var(--light-rgb)) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Ensure legend text is visible in dark mode */
        [data-theme-mode="dark"] .calendar-legend h6,
        [data-theme-mode="dark"] .calendar-legend span {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Event borders for improved visual distinction */
        .fc-event {
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 4px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        /* Enhanced borders for dark mode */
        [data-theme-mode="dark"] .fc-event {
            border: 2px solid rgba(255, 255, 255, 0.4) !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        }

        /* Specific border colors for different event types to improve contrast */
        .fc-event[style*="background-color: rgb(251, 191, 36)"],
        .fc-event[style*="background-color:#fbbf24"] {
            /* Pending events (yellow) - darker border for better contrast */
            border-color: rgba(217, 119, 6, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(35, 183, 229)"],
        .fc-event[style*="background-color:#23b7e5"] {
            /* Confirmed events (light blue) - darker blue border */
            border-color: rgba(14, 116, 144, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(239, 68, 68)"],
        .fc-event[style*="background-color:#ef4444"] {
            /* Cancelled events (red) - darker red border */
            border-color: rgba(185, 28, 28, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(140, 144, 151)"],
        .fc-event[style*="background-color:#8c9097"] {
            /* Completed/Past events (gray) - darker gray border */
            border-color: rgba(75, 85, 99, 0.6) !important;
        }

        /* Dark mode specific border adjustments */
        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(251, 191, 36)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#fbbf24"] {
            border-color: rgba(245, 158, 11, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(35, 183, 229)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#23b7e5"] {
            border-color: rgba(56, 189, 248, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(239, 68, 68)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#ef4444"] {
            border-color: rgba(248, 113, 113, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(140, 144, 151)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#8c9097"] {
            border-color: rgba(156, 163, 175, 0.8) !important;
        }

        /* Hover effect for reservations with enhanced border visibility */
        .fc-event:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
            border-width: 2px !important;
        }

        /* Dark mode hover effect */
        [data-theme-mode="dark"] .fc-event:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
        }

        /* Ensure event text remains readable with borders */
        .fc-event .fc-event-title,
        .fc-event .fc-event-time {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Dark mode text shadow adjustment */
        [data-theme-mode="dark"] .fc-event .fc-event-title,
        [data-theme-mode="dark"] .fc-event .fc-event-time {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
    </style>

    <script>
        // Cache frequently accessed DOM elements
        const DOMCache = {
            calendar: null,
            fieldFilter: null,
            calendarLoading: null,
            calendarError: null,
            timeRestrictionModal: null,
            csrfToken: null,

            // Initialize cache
            init() {
                this.calendar = document.getElementById('calendar');
                this.fieldFilter = document.getElementById('fieldFilter');
                this.calendarLoading = document.getElementById('calendarLoading');
                this.calendarError = document.getElementById('calendarError');
                this.timeRestrictionModal = document.getElementById('timeRestrictionModal');
                this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                return this.calendar && this.fieldFilter && this.calendarLoading && this.calendarError;
            },

            // Utility functions for common operations
            showLoading() {
                this.calendarLoading.style.display = 'block';
                this.calendar.style.display = 'none';
                this.calendarError.classList.add('d-none');
            },

            showCalendar() {
                this.calendarLoading.style.display = 'none';
                this.calendar.style.display = 'block';
                this.calendarError.classList.add('d-none');
            },

            showError() {
                this.calendarLoading.style.display = 'none';
                this.calendarError.classList.remove('d-none');
            }
        };

        // Initialize calendar with Moment Timezone plugin
        function initializeCalendarWhenReady() {
            console.log('=== FullCalendar Moment Timezone Implementation ===');

            // Step 0: Initialize DOM cache and verify elements are ready
            if (!DOMCache.init()) {
                console.log('DOM elements not ready, retrying in 100ms...');
                setTimeout(initializeCalendarWhenReady, 100);
                return;
            }

            // Step 1: Verify all required dependencies are available
            console.log('1. Dependency Availability:');
            const dependencies = {
                FullCalendar: typeof FullCalendar !== 'undefined',
                moment: typeof moment !== 'undefined',
                momentTimezone: typeof moment !== 'undefined' && typeof moment.tz !== 'undefined',
                FullCalendarMomentTimezone: typeof FullCalendarMomentTimezone !== 'undefined'
            };

            console.log('   - FullCalendar available:', dependencies.FullCalendar);
            console.log('   - Moment available:', dependencies.moment);
            console.log('   - Moment timezone available:', dependencies.momentTimezone);
            console.log('   - FullCalendarMomentTimezone available:', dependencies.FullCalendarMomentTimezone);

            // Check required dependencies
            if (!dependencies.FullCalendar || !dependencies.moment || !dependencies.momentTimezone || !dependencies.FullCalendarMomentTimezone) {
                console.error('Required dependencies not available, cannot proceed');
                return;
            }

            // Step 2: Test Moment Timezone support for America/Curacao
            console.log('2. Moment Timezone Support Test:');
            let curacaoTimeZone = 'America/Curacao';
            let useNowIndicator = true; // Can be enabled with Moment Timezone plugin
            let momentSupportsTimezone = false;
            let useMomentPlugin = true;

            try {
                // Test if Moment.js can handle America/Curacao timezone
                const testMoment = moment().tz('America/Curacao');
                const curacaoTime = testMoment.format('YYYY-MM-DD HH:mm:ss z');
                console.log('   ✓ America/Curacao timezone test successful:', curacaoTime);
                momentSupportsTimezone = true;

                // Calculate current times for verification
                const localTime = moment().format('YYYY-MM-DD HH:mm:ss z');
                const amsterdamTime = moment().tz('Europe/Amsterdam').format('YYYY-MM-DD HH:mm:ss z');
                console.log('   - Local time:', localTime);
                console.log('   - Amsterdam time:', amsterdamTime);
                console.log('   - Curacao time:', curacaoTime);

                // Verify time difference (should be 6 hours in August)
                const amsterdamMoment = moment().tz('Europe/Amsterdam');
                const curacaoMoment = moment().tz('America/Curacao');
                const hoursDiff = amsterdamMoment.utcOffset() - curacaoMoment.utcOffset();
                console.log('   - Time difference (minutes):', hoursDiff);
                console.log('   - Time difference (hours):', hoursDiff / 60);

            } catch (error) {
                console.log('   ✗ America/Curacao timezone test failed:', error.message);
                console.log('   - Will disable Moment Timezone plugin');
                momentSupportsTimezone = false;
                useMomentPlugin = false;
                useNowIndicator = false;
            }

            // Step 3: Determine timezone strategy
            console.log('3. Timezone Strategy:');
            if (useMomentPlugin && momentSupportsTimezone) {
                console.log('   - Using FullCalendar Moment Timezone plugin');
                console.log('   - Timezone:', curacaoTimeZone);
                console.log('   - Moment supports timezone:', momentSupportsTimezone);
                console.log('   - nowIndicator enabled with plugin support');
                console.log('   - Events will display in accurate Curacao time');
            } else {
                console.log('   - Falling back to FullCalendar native timezone support');
                console.log('   - Timezone:', curacaoTimeZone);
                console.log('   - nowIndicator disabled for safety');
                console.log('   - Events may display in UTC (fallback behavior)');
            }

            // Step 4: Build calendar configuration with Moment Timezone plugin
            console.log('4. Building Calendar Configuration:');

            const calendarConfig = {
                initialView: 'dayGridMonth',
                // Use British English locale for DD/MM/YYYY date format
                locale: 'en-gb',
                // nowIndicator enabled with Moment Timezone plugin support
                nowIndicator: useNowIndicator,
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                // Use America/Curacao timezone with Moment Timezone plugin
                timeZone: curacaoTimeZone,
                eventDisplay: 'block',
                displayEventTime: true,
                weekNumbers: true,
                weekNumberCalculation: 'ISO',
                navLinks: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                editable: false,
                selectable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = DOMCache.fieldFilter.value;
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            DOMCache.showCalendar();
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:', error);
                            DOMCache.showError();
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open reservation details in modal
                    openReservationModal(info.event.extendedProps.reservation_id);
                },
                dateClick: function(info) {
                    // Get the current view type
                    const currentView = calendar.view.type;

                    // Month view behavior: navigate to day view for the clicked date
                    if (currentView === 'dayGridMonth') {
                        // Switch to day view for the clicked date
                        calendar.changeView('timeGridDay', info.dateStr);
                    }
                    // Week/Day view behavior: open reservation creation screen
                    else if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
                        // Check if time is included (time grid views)
                        if (info.dateStr.includes('T')) {
                            const [date, time] = info.dateStr.split('T');

                            // Validate time slot is within operating hours (07:00 - 23:00)
                            if (!isWithinOperatingHours(time)) {
                                // Show time restriction modal
                                showTimeRestrictionModal();
                                return;
                            }

                            // Time is valid, proceed with reservation creation
                            const url = `/reservations/create?date=${encodeURIComponent(date)}&time=${encodeURIComponent(time)}`;
                            window.location.href = url;
                        } else {
                            // No specific time, redirect to reservation creation page with date only
                            const url = '/reservations/create?date=' + encodeURIComponent(info.dateStr);
                            window.location.href = url;
                        }
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip with reservation details
                    const props = info.event.extendedProps;
                    const pastIndicator = props.is_past ? '\n(Past Reservation)' : '';
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: XCG ${props.total_cost}\nDuration: ${props.duration} hours${pastIndicator}`;

                    // Add CSS class for past reservations
                    if (props.is_past) {
                        info.el.classList.add('past-reservation');
                    }
                },
                eventDrop: function(info) {
                    const event = info.event;

                    fetch(`/calendar/update-reservation/${event.id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': DOMCache.csrfToken
                            },
                            body: JSON.stringify({
                                start: event.startStr,
                                end: event.endStr || null
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to update reservation.');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(data.message);
                        })
                        .catch(error => {
                            alert('Error updating reservation.');
                            info.revert(); // undo the drag
                        });
                }
            };

            // Add Moment Timezone plugin conditionally
            if (useMomentPlugin && typeof FullCalendarMomentTimezone.default !== 'undefined') {
                try {
                    console.log('   - Adding Moment Timezone plugin...');
                    console.log('   - FullCalendarMomentTimezone.default type:', typeof FullCalendarMomentTimezone.default);

                    calendarConfig.plugins = [FullCalendarMomentTimezone.default];
                    console.log('   ✓ Moment Timezone plugin added to configuration');
                } catch (pluginError) {
                    console.log('   ✗ Failed to add Moment Timezone plugin:', pluginError.message);
                    console.log('   - Stack trace:', pluginError.stack);
                    useMomentPlugin = false;
                    useNowIndicator = false;
                    calendarConfig.nowIndicator = false;
                }
            }

            if (!useMomentPlugin) {
                console.log('   - Using FullCalendar native timezone support (no plugins)');
                // Ensure no plugins array to avoid any plugin-related issues
                delete calendarConfig.plugins;
            }

            console.log('   - Final configuration:', {
                timeZone: calendarConfig.timeZone,
                nowIndicator: calendarConfig.nowIndicator,
                locale: calendarConfig.locale,
                initialView: calendarConfig.initialView,
                hasPlugins: !!calendarConfig.plugins,
                pluginCount: calendarConfig.plugins ? calendarConfig.plugins.length : 0
            });

            // Step 5: Create calendar with Moment Timezone plugin or native support
            console.log('5. Creating FullCalendar Instance:');

            let calendar;
            try {
                console.log(`   - Creating FullCalendar with ${useMomentPlugin ? 'Moment Timezone plugin' : 'native timezone support'}...`);

                calendar = new FullCalendar.Calendar(DOMCache.calendar, calendarConfig);
                console.log('   ✓ FullCalendar instance created successfully');

            } catch (creationError) {
                console.error('   ✗ FullCalendar creation failed:', creationError.message);
                console.error('   - Error name:', creationError.name);
                console.error('   - Stack trace:', creationError.stack);

                // Display error message to user - no fallback needed since app requires JavaScript
                DOMCache.calendar.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #dc3545; border: 1px solid #dc3545; border-radius: 4px; background-color: #f8d7da;">
                        <h4>Calendar Loading Error</h4>
                        <p>Unable to initialize the calendar. Please refresh the page or contact support.</p>
                        <p><small>Error: ${creationError.message}</small></p>
                    </div>
                `;
                return;
            }

            // Show loading state initially
            DOMCache.showLoading();

            calendar.render();

            // Initial load complete
            setTimeout(() => {
                DOMCache.showCalendar();
            }, 1000);

            // Refresh calendar when field filter changes
            DOMCache.fieldFilter.addEventListener('change', function() {
                DOMCache.showLoading();
                calendar.refetchEvents();
            });
        }

        // Initialize calendar when DOM is ready - simplified since app requires JavaScript
        document.addEventListener('DOMContentLoaded', initializeCalendarWhenReady);

        /**
         * Check if a time is within operating hours (07:00 - 23:00)
         * Optimized version with cached constants and simplified parsing
         * @param {string} timeStr - Time string in HH:MM format
         * @returns {boolean} - True if within operating hours
         */
        const OPERATING_HOURS = {
            OPENING_MINUTES: 420, // 7 * 60 (07:00 in minutes)
            CLOSING_MINUTES: 1380 // 23 * 60 (23:00 in minutes)
        };

        function isWithinOperatingHours(timeStr) {
            // Extract hour directly from string (more efficient than split)
            const hour = parseInt(timeStr.substring(0, 2), 10);
            const minute = parseInt(timeStr.substring(3, 5), 10);

            // Convert to minutes for comparison
            const timeInMinutes = hour * 60 + minute;

            // Check if time is within operating hours
            return timeInMinutes >= OPERATING_HOURS.OPENING_MINUTES && timeInMinutes < OPERATING_HOURS.CLOSING_MINUTES;
        }

        /**
         * Show the time restriction information modal
         */
        function showTimeRestrictionModal() {
            const modal = new bootstrap.Modal(DOMCache.timeRestrictionModal);
            modal.show();
        }
    </script>
@endsection
